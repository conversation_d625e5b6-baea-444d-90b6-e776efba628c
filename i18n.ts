import { notFound } from 'next/navigation';
import { getRequestConfig } from 'next-intl/server';

// Define the list of supported locales
export const locales = ['en', 'es'] as const;

export default getRequestConfig(async ({ locale }) => {
  // Validate that the incoming `locale` parameter is valid
  if (!locales.includes(locale as any)) notFound();

  try {
    // Try to load the messages for the specified locale
    const messages = (await import(`./messages/${locale}.json`)).default;
    return {
      locale,
      messages
    };
  } catch (error) {
    console.error(`Failed to load messages for locale: ${locale}`, error);

    // Fallback to default locale if the requested locale fails to load
    if (locale !== 'en') {
      try {
        const defaultMessages = (await import('./messages/en.json')).default;
        return {
          locale: 'en',
          messages: defaultMessages
        };
      } catch (fallbackError) {
        console.error('Failed to load fallback messages:', fallbackError);
      }
    }

    // If we can't load any messages, return an empty object to prevent build errors
    return {
      locale: 'en',
      messages: {}
    };
  }
});
