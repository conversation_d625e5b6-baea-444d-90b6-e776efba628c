import { NextResponse } from 'next/server';
import createIntlMiddleware from 'next-intl/middleware';
import type { NextRequest } from 'next/server';

// Define public routes that don't require authentication
const publicRoutes = ['/', '/login', '/register', '/forgot-password', '/services', '/about', '/contact'];

// Initialize next-intl middleware
const intlMiddleware = createIntlMiddleware({
  locales: ['en', 'es'],
  defaultLocale: 'es',
  localePrefix: 'always'
});

export default function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  
  // Skip authentication for public routes and API routes
  const isPublicRoute = publicRoutes.some(route =>
    pathname === `/${request.nextUrl.locale}${route}` ||
    pathname === route ||
    pathname.startsWith('/api/') ||
    pathname === '/en' ||
    pathname === '/es' ||
    pathname === '/'
  );

  // Check for auth token in cookies
  const authToken = request.cookies.get('auth-token');
  
  // Redirect to login if trying to access protected route without auth
  if (!isPublicRoute && !authToken && !pathname.includes('.')) {
    const url = new URL('/login', request.url);
    url.searchParams.set('callbackUrl', pathname);
    return NextResponse.redirect(url);
  }

  // Redirect to dashboard if logged in and trying to access auth pages
  if (authToken && (pathname.endsWith('/login') || pathname.endsWith('/register'))) {
    return NextResponse.redirect(new URL('/dashboard', request.url));
  }

  // Apply next-intl middleware
  return intlMiddleware(request);
}

export const config = {
  // Match all paths except for static files and internal Next.js files
  matcher: [
    '/((?!api|_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
};
