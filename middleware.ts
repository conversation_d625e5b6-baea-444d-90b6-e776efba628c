import createIntlMiddleware from 'next-intl/middleware';
import { locales } from './i18n';

// Create the internationalization middleware
const intlMiddleware = createIntlMiddleware({
  locales,
  defaultLocale: 'es',
  localePrefix: 'always'
});

export default intlMiddleware;

export const config = {
  // Match all paths except for static files and internal Next.js files
  matcher: [
    '/((?!api|_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
};
