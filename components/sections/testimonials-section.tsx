'use client';

import React from 'react';
import { useTranslations } from 'next-intl';

export function TestimonialsSection() {
  const t = useTranslations('testimonials');

  return (
    <section id="testimonials" className="py-20 relative overflow-hidden">

      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          <h2 className="text-4xl lg:text-6xl font-bold text-white mb-6">
            <span className="bg-gradient-to-r from-yellow-400 via-orange-500 to-yellow-400 bg-clip-text text-transparent animate-pulse-slow">
              {t('title')}
            </span>
          </h2>
          <p className="mt-6 max-w-3xl text-xl text-blue-100 mx-auto leading-relaxed">
            {t('subtitle')}
          </p>
        </div>
        {/* Testimonials will be implemented here */}
        <div className="mt-20 text-center">
          <p className="text-blue-200">Testimonials section - To be implemented</p>
        </div>
      </div>
    </section>
  );
}
