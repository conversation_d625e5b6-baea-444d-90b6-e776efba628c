'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Star, 
  MapPin, 
  Clock, 
  Award, 
  Users, 
  Phone, 
  Mail,
  Calendar,
  MessageSquare,
  Filter,
  Search,
  User,
  CheckCircle2 as Verified,
  TrendingUp,
  DollarSign,
  Languages,
  GraduationCap,
  Building
} from 'lucide-react';

const lawyers = [
  {
    id: 1,
    name: '<PERSON><PERSON>. <PERSON>',
    title: 'Especialista en Derecho de Familia',
    image: '/api/placeholder/150/150',
    rating: 4.9,
    reviews: 127,
    experience: 15,
    location: 'San Juan, PR',
    specializations: ['Derecho de Familia', 'Divorcio', 'Custodia'],
    languages: ['Español', 'Inglés'],
    education: 'Universidad de Puerto Rico, Escuela de Derecho',
    hourlyRate: 250,
    responseTime: '< 2 horas',
    casesWon: 234,
    isVerified: true,
    isOnline: true,
    bio: 'Más de 15 años de experiencia en derecho de familia. Especializada en casos complejos de divorcio y custodia de menores.',
    achievements: ['Top 10 Abogados de Familia 2023', 'Certificada en Mediación Familiar'],
    availability: 'Disponible esta semana'
  },
  {
    id: 2,
    name: 'Lic. Carlos Alberto Rodríguez',
    title: 'Experto en Derecho Inmobiliario',
    image: '/api/placeholder/150/150',
    rating: 4.8,
    reviews: 89,
    experience: 12,
    location: 'Bayamón, PR',
    specializations: ['Derecho Inmobiliario', 'Contratos', 'Títulos'],
    languages: ['Español', 'Inglés'],
    education: 'Universidad Interamericana, Escuela de Derecho',
    hourlyRate: 200,
    responseTime: '< 4 horas',
    casesWon: 156,
    isVerified: true,
    isOnline: false,
    bio: 'Especialista en transacciones inmobiliarias complejas y resolución de disputas de propiedad.',
    achievements: ['Mejor Abogado Inmobiliario 2022', 'Miembro del Colegio de Abogados'],
    availability: 'Próxima cita: Mañana'
  },
  {
    id: 3,
    name: 'Lic. Ana Isabel Martínez',
    title: 'Defensora Penal Experimentada',
    image: '/api/placeholder/150/150',
    rating: 4.7,
    reviews: 203,
    experience: 18,
    location: 'Ponce, PR',
    specializations: ['Derecho Penal', 'Defensa Criminal', 'Apelaciones'],
    languages: ['Español', 'Inglés', 'Francés'],
    education: 'Universidad de Harvard, Escuela de Derecho',
    hourlyRate: 350,
    responseTime: '< 1 hora',
    casesWon: 312,
    isVerified: true,
    isOnline: true,
    bio: 'Defensora penal con amplia experiencia en casos federales y estatales. Graduada de Harvard Law School.',
    achievements: ['Super Lawyer 2023', 'Mejor Defensora Penal del Sur'],
    availability: 'Disponible hoy'
  },
  {
    id: 4,
    name: 'Dr. Roberto Luis Sánchez',
    title: 'Especialista en Derecho Corporativo',
    image: '/api/placeholder/150/150',
    rating: 4.9,
    reviews: 67,
    experience: 20,
    location: 'San Juan, PR',
    specializations: ['Derecho Corporativo', 'M&A', 'Compliance'],
    languages: ['Español', 'Inglés'],
    education: 'Universidad de Columbia, Escuela de Derecho',
    hourlyRate: 450,
    responseTime: '< 3 horas',
    casesWon: 89,
    isVerified: true,
    isOnline: true,
    bio: 'Experto en fusiones y adquisiciones con más de 20 años asesorando empresas Fortune 500.',
    achievements: ['Chambers & Partners Ranked', 'Legal 500 Recommended'],
    availability: 'Disponible esta semana'
  },
  {
    id: 5,
    name: 'Lic. Carmen Rosa Vega',
    title: 'Abogada Laboral Certificada',
    image: '/api/placeholder/150/150',
    rating: 4.6,
    reviews: 145,
    experience: 10,
    location: 'Mayagüez, PR',
    specializations: ['Derecho Laboral', 'Discriminación', 'Compensación'],
    languages: ['Español', 'Inglés'],
    education: 'Universidad de Puerto Rico, Escuela de Derecho',
    hourlyRate: 180,
    responseTime: '< 6 horas',
    casesWon: 178,
    isVerified: true,
    isOnline: false,
    bio: 'Defensora apasionada de los derechos de los trabajadores con un historial probado de éxito.',
    achievements: ['Abogada del Año 2021', 'Certificada en Derecho Laboral'],
    availability: 'Próxima cita: Esta tarde'
  },
  {
    id: 6,
    name: 'Dr. Miguel Ángel Torres',
    title: 'Especialista en Lesiones Personales',
    image: '/api/placeholder/150/150',
    rating: 4.8,
    reviews: 234,
    experience: 16,
    location: 'Caguas, PR',
    specializations: ['Lesiones Personales', 'Negligencia Médica', 'Accidentes'],
    languages: ['Español', 'Inglés'],
    education: 'Universidad de Georgetown, Escuela de Derecho',
    hourlyRate: 0, // Contingency fee
    responseTime: '< 2 horas',
    casesWon: 267,
    isVerified: true,
    isOnline: true,
    bio: 'Especialista en lesiones personales con más de $50M recuperados para clientes.',
    achievements: ['Million Dollar Advocates Forum', 'Top 100 Trial Lawyers'],
    availability: 'Consulta gratuita hoy'
  }
];

const specializations = [
  'Todas las especialidades',
  'Derecho de Familia',
  'Derecho Inmobiliario',
  'Derecho Penal',
  'Derecho Corporativo',
  'Derecho Laboral',
  'Lesiones Personales',
  'Derecho de Inmigración',
  'Derecho Tributario'
];

const locations = [
  'Todas las ubicaciones',
  'San Juan',
  'Bayamón',
  'Ponce',
  'Mayagüez',
  'Caguas',
  'Arecibo',
  'Guaynabo'
];

export default function LawyerDirectory() {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedSpecialization, setSelectedSpecialization] = useState('Todas las especialidades');
  const [selectedLocation, setSelectedLocation] = useState('Todas las ubicaciones');
  const [sortBy, setSortBy] = useState('rating');
  const [showFilters, setShowFilters] = useState(false);

  const filteredLawyers = lawyers.filter(lawyer => {
    const matchesSearch = lawyer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         lawyer.specializations.some(spec => spec.toLowerCase().includes(searchTerm.toLowerCase()));
    const matchesSpecialization = selectedSpecialization === 'Todas las especialidades' ||
                                 lawyer.specializations.includes(selectedSpecialization);
    const matchesLocation = selectedLocation === 'Todas las ubicaciones' ||
                           lawyer.location.includes(selectedLocation);
    
    return matchesSearch && matchesSpecialization && matchesLocation;
  });

  const sortedLawyers = [...filteredLawyers].sort((a, b) => {
    switch (sortBy) {
      case 'rating':
        return b.rating - a.rating;
      case 'experience':
        return b.experience - a.experience;
      case 'price-low':
        return a.hourlyRate - b.hourlyRate;
      case 'price-high':
        return b.hourlyRate - a.hourlyRate;
      default:
        return 0;
    }
  });

  return (
    <section className="relative py-20 overflow-hidden bg-gradient-to-b from-gray-900 to-black">
      {/* Animated background elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_center,_var(--tw-gradient-stops))] from-amber-500/10 via-transparent to-transparent w-full h-full"></div>
        <div className="absolute inset-0 bg-grid-amber-500/[0.05] bg-[size:40px_40px] opacity-50"></div>
      </div>

      {/* Grid Pattern Overlay */}
      <div className="absolute inset-0 opacity-40" style={{
        backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
      }}></div>

      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="container mx-auto px-4 relative z-10">
          <div className="text-center mb-12">
            <Badge className="mb-4 bg-amber-500/20 text-amber-400 hover:bg-amber-500/30 border border-amber-500/30 text-sm font-medium">
              <Users className="w-4 h-4 mr-2" />
              Nuestros Abogados
            </Badge>
            <h2 className="text-4xl font-bold text-white mb-4">Encuentra tu Abogado Ideal</h2>
            <p className="text-lg text-gray-200 max-w-2xl mx-auto">
              Conectamos a los puertorriqueños con los mejores abogados de la isla. 
              Filtra por especialidad, ubicación y más para encontrar el profesional legal perfecto para tu caso.
            </p>
          </div>

          {/* Search and Filters */}
          <Card className="mb-8 bg-gray-900/40 backdrop-blur-2xl border border-gray-700/50 shadow-2xl">
            <CardContent className="p-6">
              <div className="flex flex-col lg:flex-row gap-4">
                <div className="grid grid-cols-1 md:grid-cols-4 gap-6 w-full">
                  <div className="md:col-span-3 relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-amber-400" />
                    <Input
                      type="text"
                      placeholder="Buscar abogados, especialidades..."
                      className="pl-10 bg-gray-800/80 border-gray-700 text-white placeholder-gray-400 focus:ring-2 focus:ring-amber-500 focus:border-transparent"
                    />
                  </div>
                  <Select>
                    <SelectTrigger className="bg-gray-800/80 border-gray-700 text-white hover:bg-gray-700/80">
                      <SelectValue placeholder="Filtrar por ubicación" />
                    </SelectTrigger>
                    <SelectContent className="bg-gray-800 border-gray-700 text-white">
                      {locations.map((location) => (
                        <SelectItem key={location} value={location} className="hover:bg-amber-500/20 focus:bg-amber-500/20">
                          {location}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <Select value={selectedSpecialization} onValueChange={setSelectedSpecialization}>
                  <SelectTrigger className="w-full sm:w-[200px]">
                    <SelectValue placeholder="Especialidad" />
                  </SelectTrigger>
                  <SelectContent>
                    {specializations.map((spec) => (
                      <SelectItem key={spec} value={spec}>{spec}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Select value={selectedLocation} onValueChange={setSelectedLocation}>
                  <SelectTrigger className="w-full sm:w-[180px]">
                    <SelectValue placeholder="Ubicación" />
                  </SelectTrigger>
                  <SelectContent>
                    {locations.map((location) => (
                      <SelectItem key={location} value={location}>{location}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Select value={sortBy} onValueChange={setSortBy}>
                  <SelectTrigger className="w-full sm:w-[150px]">
                    <SelectValue placeholder="Ordenar por" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="rating">Calificación</SelectItem>
                    <SelectItem value="experience">Experiencia</SelectItem>
                    <SelectItem value="price-low">Precio: Menor</SelectItem>
                    <SelectItem value="price-high">Precio: Mayor</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Lawyers Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {sortedLawyers.map((lawyer) => (
              <Card key={lawyer.id} className="bg-gray-800/80 border border-gray-700 hover:border-amber-500/50 transition-all duration-300 hover:shadow-lg hover:shadow-amber-500/10 backdrop-blur-sm">
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div className="flex items-center space-x-4">
                      <div className="relative">
                        <div className="w-16 h-16 rounded-full bg-gradient-to-br from-amber-500 to-amber-600 flex items-center justify-center text-2xl font-bold text-white">
                          {lawyer.name.split(' ').map(n => n[0]).join('').toUpperCase()}
                        </div>
                        {lawyer.isOnline && (
                          <div className="absolute bottom-0 right-0 w-3.5 h-3.5 bg-green-500 rounded-full border-2 border-gray-800"></div>
                        )}
                      </div>
                      <div>
                        <div className="flex items-center">
                          <h3 className="text-lg font-semibold text-white">{lawyer.name}</h3>
                          {lawyer.isVerified && (
                            <Verified className="w-4 h-4 ml-1.5 text-amber-400" />
                          )}
                        </div>
                        <p className="text-sm text-amber-400 mt-0.5">{lawyer.title}</p>
                        <div className="flex items-center mt-1">
                          <div className="flex items-center">
                            <Star className="w-4 h-4 text-amber-400 fill-amber-400/30" />
                            <span className="ml-1 text-sm font-medium text-white">{lawyer.rating}</span>
                            <span className="mx-1.5 text-gray-500">•</span>
                            <span className="text-sm text-gray-300">{lawyer.reviews} reseñas</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex items-center text-sm text-gray-200">
                      <MapPin className="w-4 h-4 mr-2 text-amber-400" />
                      <span>{lawyer.location}</span>
                    </div>
                  
                    {/* Bio */}
                    <p className="text-sm text-gray-600 line-clamp-2">{lawyer.bio}</p>
                  
                    {/* Pricing */}
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="text-lg font-bold text-gray-900">
                          {lawyer.hourlyRate === 0 ? 'Sin costo inicial' : `$${lawyer.hourlyRate}/hora`}
                        </div>
                        <div className="text-xs text-green-600">{lawyer.availability}</div>
                      </div>
                      <div className="flex space-x-2">
                        <Button size="sm" variant="outline">
                          <MessageSquare className="h-4 w-4" />
                        </Button>
                        <Button size="sm" className="btn-legal">
                          <Calendar className="h-4 w-4 mr-1" />
                          Cita
                        </Button>
                      </div>
                    </div>
                  
                    {/* Achievements */}
                    {lawyer.achievements.length > 0 && (
                      <div className="pt-3 border-t border-gray-100">
                        <div className="text-xs text-gray-500 mb-1">Reconocimientos:</div>
                        <div className="text-xs text-blue-600">
                          {lawyer.achievements[0]}
                        </div>
                      </div>
                    )}
                  </div>
                </CardContent>
            </Card>
          ))}
        </div>

        {/* Load More */}
        <div className="text-center mt-12">
          <Button variant="outline" size="lg">
            Cargar Más Abogados
          </Button>
        </div>
        </div>
      </div>
    </section>
  );
}
