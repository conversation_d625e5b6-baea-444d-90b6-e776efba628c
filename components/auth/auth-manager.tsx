'use client';

import { useState } from 'react';
import LoginPopup from './login-popup';
import RegisterPopup from './register-popup';

interface AuthManagerProps {
  children: (props: {
    openLogin: () => void;
    openRegister: () => void;
    isLoginOpen: boolean;
    isRegisterOpen: boolean;
  }) => React.ReactNode;
}

export default function AuthManager({ children }: AuthManagerProps) {
  const [isLoginOpen, setIsLoginOpen] = useState(false);
  const [isRegisterOpen, setIsRegisterOpen] = useState(false);

  const openLogin = () => {
    setIsRegisterOpen(false);
    setIsLoginOpen(true);
  };

  const openRegister = () => {
    setIsLoginOpen(false);
    setIsRegisterOpen(true);
  };

  const closeAll = () => {
    setIsLoginOpen(false);
    setIsRegisterOpen(false);
  };

  return (
    <>
      {children({
        openLogin,
        openRegister,
        isLoginOpen,
        isRegisterOpen,
      })}
      
      <LoginPopup
        isOpen={isLoginOpen}
        onClose={closeAll}
        onSwitchToRegister={openRegister}
      />
      
      <RegisterPopup
        isOpen={isRegisterOpen}
        onClose={closeAll}
        onSwitchToLogin={openLogin}
      />
    </>
  );
}
