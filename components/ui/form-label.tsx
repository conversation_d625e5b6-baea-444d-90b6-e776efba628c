"use client"

import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const formLabelVariants = cva(
  "text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
)

const FormLabel = React.forwardRef<
  HTMLLabelElement,
  React.LabelHTMLAttributes<HTMLLabelElement> &
    VariantProps<typeof formLabelVariants>
>(({ className, ...props }, ref) => (
  <label
    ref={ref}
    className={cn(formLabelVariants(), className)}
    {...props}
  />
))

FormLabel.displayName = "FormLabel"

export { FormLabel }
