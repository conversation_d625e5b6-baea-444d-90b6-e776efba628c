import { setRequestLocale } from 'next-intl/server';
import ServicesPage from '@/components/pages/services-page';

// Force dynamic rendering to prevent Firebase initialization during build
export const dynamic = 'force-dynamic';

interface ServicesPageProps {
  params: { locale: string };
}

export default function Services({ params: { locale } }: ServicesPageProps) {
  setRequestLocale(locale);
  
  return <ServicesPage />;
}
