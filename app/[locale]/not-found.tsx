import { useTranslations } from 'next-intl';
import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';

export default function NotFound() {
  const t = useTranslations('NotFound');
  
  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gray-50 dark:bg-gray-900 p-4">
      <div className="text-center max-w-md space-y-6">
        <h1 className="text-6xl font-bold text-yellow-500">404</h1>
        <h2 className="text-3xl font-semibold text-gray-900 dark:text-white">
          {t('title')}
        </h2>
        <p className="text-lg text-gray-600 dark:text-gray-300">
          {t('description')}
        </p>
        <div className="pt-4">
          <Link href="/">
            <Button className="bg-yellow-500 hover:bg-yellow-600 text-white">
              {t('backToHome')}
            </Button>
          </Link>
        </div>
      </div>
    </div>
  );
}
