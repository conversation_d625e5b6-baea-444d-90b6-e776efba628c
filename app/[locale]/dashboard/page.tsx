import { setRequestLocale } from 'next-intl/server';
import DashboardPage from '@/components/pages/dashboard-page';

// Force dynamic rendering to prevent Firebase initialization during build
export const dynamic = 'force-dynamic';

interface DashboardPageProps {
  params: { locale: string };
}

export default function Dashboard({ params: { locale } }: DashboardPageProps) {
  setRequestLocale(locale);
  
  return <DashboardPage />;
}
