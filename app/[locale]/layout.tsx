import { notFound } from 'next/navigation';
import { setRequestLocale } from 'next-intl/server';
import { AuthProvider } from '@/contexts/auth-context';

const locales = ['en', 'es'];

interface LocaleLayoutProps {
  children: React.ReactNode;
  params: { locale: string };
}

export default function LocaleLayout({
  children,
  params: { locale }
}: LocaleLayoutProps) {
  // Validate that the incoming `locale` parameter is valid
  if (!locales.includes(locale as any)) notFound();

  setRequestLocale(locale);

  return (
    <AuthProvider>
      {children}
    </AuthProvider>
  );
}

export function generateStaticParams() {
  return locales.map((locale) => ({ locale }));
}
