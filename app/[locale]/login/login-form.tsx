'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import Link from 'next/link';
import { Mail, Lock, AlertCircle, Loader2, Scale } from 'lucide-react';
import { useAuth } from '@/contexts/auth-context';
import { useTranslations } from 'next-intl';

export default function LoginForm() {
  const t = useTranslations('auth');
  const router = useRouter();
  const { user, login, isLoading } = useAuth();
  const [error, setError] = useState<string | null>(null);
  const [formData, setFormData] = useState({
    email: '',
    password: '',
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Redirect if already logged in
  useEffect(() => {
    if (user && !isLoading) {
      router.push('/dashboard');
    }
  }, [user, isLoading, router]);

  // Get error message translation
  const getErrorMessage = (error: unknown): string => {
    console.log('Login error:', error); // Debug log
    if (error instanceof Error) {
      const errorMessage = error.message.toLowerCase();
      if (errorMessage.includes('email') || errorMessage.includes('user-not-found')) {
        return t('errors.invalidEmail');
      } else if (errorMessage.includes('password') || errorMessage.includes('wrong-password')) {
        return t('errors.invalidCredentials');
      } else if (errorMessage.includes('too-many-requests')) {
        return t('errors.tooManyAttempts');
      } else if (errorMessage.includes('network')) {
        return t('errors.networkError');
      }
      return error.message || t('errors.unknownError');
    }
    return t('errors.unknownError');
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (isSubmitting) return;

    // Basic validation
    if (!formData.email || !formData.password) {
      setError(t('errors.requiredField'));
      return;
    }

    setError(null);
    setIsSubmitting(true);

    try {
      await login(formData.email, formData.password);
      // The AuthProvider will handle the redirect
    } catch (err) {
      console.error('Login error:', err);
      setError(getErrorMessage(err));
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
  };

  return (
    <div className="w-full max-w-md space-y-6">
      <div className="text-center">
        <div className="flex justify-between items-center mb-6">
          <Link 
            href="/register" 
            className="px-4 py-2 text-sm font-medium text-white bg-yellow-500 hover:bg-yellow-600 rounded-lg transition-colors duration-200"
          >
            Registrarse
          </Link>
          <div className="flex-1 flex justify-center">
            <Scale className="h-12 w-12 text-yellow-500" />
          </div>
          <div className="w-24"></div> {/* Spacer for alignment */}
        </div>
        <h2 className="mt-6 text-3xl font-extrabold text-gray-900 dark:text-white">
          {t('signIn')}
        </h2>
        <p className="mt-2 text-sm text-gray-700 dark:text-gray-300">
          {t.rich('haveAccount', {
            link: (chunks) => (
              <Link 
                href="/register" 
                className="font-medium text-yellow-600 hover:text-yellow-500 dark:text-yellow-400 dark:hover:text-yellow-300"
              >
                {chunks}
              </Link>
            )
          })}
        </p>
      </div>

      {error && (
        <div className="rounded-md bg-red-50 p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <AlertCircle className="h-5 w-5 text-red-400" aria-hidden="true" />
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">{error}</h3>
            </div>
          </div>
        </div>
      )}

      <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
        <div className="space-y-4">
          <div>
            <label htmlFor="email-address" className="block text-sm font-medium text-gray-700 dark:text-gray-200 mb-1">
              {t('email')}
            </label>
            <Input
              id="email-address"
              name="email"
              type="email"
              autoComplete="email"
              required
              value={formData.email}
              onChange={handleChange}
              className="appearance-none block w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              placeholder={t('email')}
              disabled={isLoading || isSubmitting}
            />
          </div>
          <div>
            <div className="flex items-center justify-between mb-1">
              <label htmlFor="password" className="block text-sm font-medium text-gray-700 dark:text-gray-200">
                {t('password')}
              </label>
              <div className="text-sm">
                <Link
                  href="/forgot-password"
                  className="font-medium text-yellow-600 hover:text-yellow-500 dark:text-yellow-400 dark:hover:text-yellow-300"
                >
                  {t('forgotPassword')}
                </Link>
              </div>
            </div>
            <Input
              id="password"
              name="password"
              type="password"
              autoComplete="current-password"
              required
              value={formData.password}
              onChange={handleChange}
              className="appearance-none block w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              placeholder={t('password')}
              disabled={isLoading || isSubmitting}
            />
          </div>
        </div>

        <div className="flex items-center">
          <input
            id="remember-me"
            name="remember-me"
            type="checkbox"
            className="h-4 w-4 text-yellow-600 focus:ring-yellow-500 border-gray-300 rounded dark:border-gray-600 dark:bg-gray-700 dark:checked:bg-yellow-500"
          />
          <label
            htmlFor="remember-me"
            className="ml-2 block text-sm text-gray-700 dark:text-gray-300"
          >
            {t('rememberMe')}
          </label>
        </div>

        <div>
          <Button
            type="submit"
            className="w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-gradient-to-r from-yellow-500 to-yellow-600 hover:from-yellow-600 hover:to-yellow-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
            disabled={isLoading || isSubmitting}
          >
            {isSubmitting ? (
              <div className="flex items-center">
                <Loader2 className="animate-spin -ml-1 mr-2 h-4 w-4" />
                {t('signingIn')}
              </div>
            ) : (
              t('login')
            )}
          </Button>
        </div>
      </form>
    </div>
  );
}
