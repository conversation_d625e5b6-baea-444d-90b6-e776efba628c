'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { FormLabel } from '@/components/ui/form-label';
import Link from 'next/link';
import { Mail, Lock, User, Phone, ArrowRight, AlertCircle, CheckCircle, Loader2 } from 'lucide-react';
import { useAuth } from '@/contexts/auth-context';

export default function RegisterPage() {
  const router = useRouter();
  const { user, register, isLoading } = useAuth();
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    password: '',
    confirmPassword: '',
  });
  
  // Redirect if already logged in
  useEffect(() => {
    if (user && !isLoading) {
      router.push('/dashboard');
    }
  }, [user, isLoading, router]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    
    const { firstName, lastName, email, phone, password, confirmPassword } = formData;
    
    // Validate required fields
    if (!firstName || !lastName || !email || !phone || !password || !confirmPassword) {
      setError('Por favor completa todos los campos');
      return;
    }
    
    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      setError('Por favor ingresa un correo electrónico válido');
      return;
    }
    
    // Validate phone format (basic validation)
    const phoneRegex = /^[0-9\-\+\(\)\s]{10,15}$/;
    if (!phoneRegex.test(phone)) {
      setError('Por favor ingresa un número de teléfono válido');
      return;
    }
    
    // Validate password match
    if (password !== confirmPassword) {
      setError('Las contraseñas no coinciden');
      return;
    }
    
    // Validate password strength
    if (password.length < 8) {
      setError('La contraseña debe tener al menos 8 caracteres');
      return;
    }
    
    try {
      const result = await register({
        firstName,
        lastName,
        email,
        phone,
        password,
      });
      
      if (result.success) {
        setSuccess(true);
        // Auto-redirect to dashboard after 2 seconds
        setTimeout(() => {
          router.push('/dashboard');
        }, 2000);
      } else {
        setError(result.error || 'Error al registrar la cuenta. Por favor, inténtalo de nuevo.');
      }
    } catch (err) {
      console.error('Registration error:', err);
      setError('Ocurrió un error inesperado. Por favor, inténtalo de nuevo.');
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-b from-gray-900 to-black p-4">
      <div className="w-full max-w-md bg-gray-800/80 backdrop-blur-sm rounded-2xl p-8 border border-gray-700 shadow-2xl">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-white mb-2">Crea tu cuenta</h1>
          <p className="text-gray-400">Completa el formulario para registrarte</p>
          
          {error && (
            <div className="mt-4 p-3 bg-red-500/10 border border-red-500/30 rounded-lg text-red-400 text-sm flex items-start">
              <AlertCircle className="h-5 w-5 mr-2 mt-0.5 flex-shrink-0" />
              <span>{error}</span>
            </div>
          )}
          
          {success && (
            <div className="mt-4 p-3 bg-green-500/10 border border-green-500/30 rounded-lg text-green-400 text-sm flex items-start">
              <CheckCircle className="h-5 w-5 mr-2 mt-0.5 flex-shrink-0" />
              <span>¡Cuenta creada exitosamente! Redirigiendo al panel de control...</span>
            </div>
          )}
        </div>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <FormLabel htmlFor="firstName" className="text-gray-300 mb-1 block">
                Nombre
              </FormLabel>
              <div className="relative">
                <User className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  id="firstName"
                  name="firstName"
                  type="text"
                  placeholder="Juan"
                  value={formData.firstName}
                  onChange={handleChange}
                  className="w-full bg-gray-800 border-gray-700 text-white placeholder-gray-500 focus:ring-2 focus:ring-amber-500 focus:border-transparent"
                  required
                  disabled={isLoading || success}
                />
              </div>
            </div>
            <div>
              <FormLabel htmlFor="lastName" className="text-gray-300 mb-1 block">
                Apellido
              </FormLabel>
              <Input
                id="lastName"
                name="lastName"
                type="text"
                placeholder="Pérez"
                value={formData.lastName}
                onChange={handleChange}
                className="w-full bg-gray-800 border-gray-700 text-white placeholder-gray-500 focus:ring-2 focus:ring-amber-500 focus:border-transparent"
                required
                disabled={isLoading || success}
              />
            </div>
          </div>
          
          <div>
            <FormLabel htmlFor="email" className="text-gray-300 mb-1 block">
              Correo Electrónico
            </FormLabel>
            <div className="relative">
              <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                id="email"
                name="email"
                type="email"
                placeholder="<EMAIL>"
                value={formData.email}
                onChange={handleChange}
                className="w-full bg-gray-800 border-gray-700 text-white placeholder-gray-500 focus:ring-2 focus:ring-amber-500 focus:border-transparent"
                required
                disabled={isLoading || success}
              />
            </div>
          </div>
          
          <div>
            <FormLabel htmlFor="phone" className="text-gray-300 mb-1 block">
              Teléfono
            </FormLabel>
            <div className="relative">
              <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                id="phone"
                name="phone"
                type="tel"
                placeholder="+****************"
                value={formData.phone}
                onChange={handleChange}
                className="w-full bg-gray-800 border-gray-700 text-white placeholder-gray-500 focus:ring-2 focus:ring-amber-500 focus:border-transparent"
                required
                disabled={isLoading || success}
              />
            </div>
          </div>
          
          <div>
            <FormLabel htmlFor="password" className="text-gray-300 mb-1 block">
              Contraseña
            </FormLabel>
            <div className="relative">
              <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                id="password"
                name="password"
                type="password"
                placeholder="••••••••"
                value={formData.password}
                onChange={handleChange}
                className="w-full bg-gray-800 border-gray-700 text-white placeholder-gray-500 focus:ring-2 focus:ring-amber-500 focus:border-transparent"
                required
                disabled={isLoading || success}
              />
            </div>
          </div>
          
          <div>
            <FormLabel htmlFor="confirmPassword" className="text-gray-300 mb-1 block">
              Confirmar Contraseña
            </FormLabel>
            <div className="relative">
              <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                id="confirmPassword"
                name="confirmPassword"
                type="password"
                placeholder="••••••••"
                value={formData.confirmPassword}
                onChange={(e) => setFormData(prev => ({ ...prev, confirmPassword: e.target.value }))}
                className="w-full bg-gray-800 border-gray-700 text-white placeholder-gray-500 focus:ring-2 focus:ring-amber-500 focus:border-transparent"
                required
                disabled={isLoading || success}
              />
            </div>
          </div>
          
          <div className="pt-2">
            <Button 
              type="submit" 
              className="w-full bg-gradient-to-r from-amber-500 to-amber-600 hover:from-amber-600 hover:to-amber-700 text-white shadow-md shadow-amber-500/20 hover:shadow-amber-500/30 transition-all"
              disabled={isLoading || success}
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Creando cuenta...
                </>
              ) : success ? (
                <>
                  <CheckCircle className="mr-2 h-4 w-4" />
                  ¡Registro exitoso!
                </>
              ) : (
                <>
                  Registrarme
                  <ArrowRight className="ml-2 h-4 w-4" />
                </>
              )}
            </Button>
            
            <div className="text-center text-sm text-gray-400 mt-4">
              ¿Ya tienes una cuenta?{' '}
              <Link href="/login" className="text-amber-400 hover:text-amber-300 font-medium transition-colors">
                Inicia sesión aquí
              </Link>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
}
