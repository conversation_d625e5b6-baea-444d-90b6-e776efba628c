'use client';

import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
import { useAuth } from '@/contexts/auth-context';

type User = {
  id: string;
  name: string;
  email: string;
  phone?: string;
} | null;

interface DashboardCardProps {
  title: string;
  description: string;
}

const DashboardCard: React.FC<DashboardCardProps> = ({ title, description }) => (
  <div className="bg-gray-700 rounded-lg p-6 shadow hover:bg-gray-650 transition-colors">
    <h3 className="text-lg font-semibold text-amber-400 mb-2">{title}</h3>
    <p className="text-gray-300">{description}</p>
  </div>
);

export default function DashboardPage() {
  const router = useRouter();
  const auth = useAuth();

  // Redirect to login if not authenticated
  useEffect(() => {
    if (auth && !auth.isLoading && !auth.user) {
      router.push('/login');
    }
  }, [auth, router]);

  if (!auth || auth.isLoading || !auth.user) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-amber-400"></div>
      </div>
    );
  }

  const { user, logout } = auth as { user: User; logout: () => Promise<void>; };

  const handleLogout = async () => {
    try {
      await logout();
      router.push('/login');
    } catch (error) {
      console.error('Error al cerrar sesión:', error);
    }
  };

  return (
    <div className="min-h-screen bg-gray-900 text-white">
      <header className="bg-gray-800 shadow-lg">
        <div className="max-w-7xl mx-auto px-4 py-4 sm:px-6 lg:px-8 flex justify-between items-center">
          <h1 className="text-2xl font-bold text-amber-400">Abogo</h1>
          <div className="flex items-center space-x-4">
            <span className="text-gray-300">Hola, {user?.name || user?.email || 'Usuario'}</span>
            <button
              onClick={handleLogout}
              className="px-4 py-2 bg-amber-600 hover:bg-amber-700 text-white rounded-md transition-colors flex items-center"
            >
              <span>Cerrar Sesión</span>
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 ml-2" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M3 3a1 1 0 00-1 1v12a1 1 0 102 0V4a1 1 0 00-1-1zm10.293 9.293a1 1 0 001.414 1.414l3-3a1 1 0 000-1.414l-3-3a1 1 0 10-1.414 1.414L14.586 9H7a1 1 0 100 2h7.586l-1.293 1.293z" clipRule="evenodd" />
              </svg>
            </button>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 py-8 sm:px-6 lg:px-8">
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-white mb-2">Panel de Control</h2>
          <p className="text-gray-400">Bienvenido a tu área personal</p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <DashboardCard 
            title="Perfil" 
            description="Gestiona tu información personal y preferencias de cuenta." 
          />
          <DashboardCard 
            title="Documentos" 
            description="Revisa y administra tus documentos legales." 
          />
          <DashboardCard 
            title="Notificaciones" 
            description="Mantente al día con tus notificaciones recientes." 
          />
          <DashboardCard 
            title="Pagos" 
            description="Revisa tu historial de pagos y facturas." 
          />
          <DashboardCard 
            title="Ayuda" 
            description="Encuentra respuestas a tus preguntas frecuentes." 
          />
          <DashboardCard 
            title="Seguridad" 
            description="Gestiona la seguridad de tu cuenta." 
          />
        </div>
      </main>
    </div>
  );
}
