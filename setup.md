# 🚀 LegalPR Complete Setup Guide

## ✅ Your Environment File is Ready!

I've created your `.env.local` file with your Firebase configuration and added PWA support for mobile installation.

## 📦 Step 1: Install Web App Dependencies

Run this command to install all required packages:

```bash
npm install
```

If you encounter any errors, try:

```bash
# Clear npm cache
npm cache clean --force

# Delete node_modules and package-lock.json
rm -rf node_modules package-lock.json

# Install again
npm install
```

## 📱 Step 2: Mobile App Setup (React Native/Expo)

The mobile app is located in the `/mobile` directory:

```bash
# Navigate to mobile directory
cd mobile

# Install Expo CLI globally (if not already installed)
npm install -g @expo/cli

# Install mobile dependencies
npm install

# Start the development server
npx expo start
```

### Mobile App Features:
- ✅ **Native iOS & Android apps**
- ✅ **Firebase Authentication**
- ✅ **Push Notifications**
- ✅ **Stripe Payments**
- ✅ **Real-time Messaging**
- ✅ **Document Upload/Download**
- ✅ **Offline Support**

## 🔧 Step 2: Complete Environment Setup

Your `.env.local` file has been created with your Firebase config. You still need to add:

### Firebase Admin SDK (for server-side operations)
1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Select your `delawpr` project
3. Go to Project Settings > Service Accounts
4. Click "Generate new private key"
5. Download the JSON file
6. Copy the `private_key` and `client_email` to your `.env.local`

### Stripe Keys (for payments)
1. Go to [Stripe Dashboard](https://dashboard.stripe.com/)
2. Get your publishable and secret keys
3. Add them to `.env.local`

### Optional Services
- **SendGrid** (for emails)
- **Google Maps API** (for lawyer locations)

## 🚀 Step 3: Start Development

```bash
# Start the Next.js development server
npm run dev
```

Your app will be available at: http://localhost:3000

## 🔥 Step 4: Firebase Setup (Optional for now)

If you want to use Firebase emulators for local development:

```bash
# Install Firebase CLI globally
npm install -g firebase-tools

# Login to Firebase
firebase login

# Initialize Firebase in your project
firebase init

# Start emulators
firebase emulators:start
```

## 📱 What's Already Working

✅ **Next.js App** with TypeScript  
✅ **Firebase Configuration** with your project  
✅ **Tailwind CSS** styling  
✅ **Internationalization** (Spanish/English)  
✅ **Authentication** pages  
✅ **Dashboard** structure  
✅ **Component Library** (ShadCN UI)  

## 🎯 Next Steps After Setup

1. **Test the app**: Visit http://localhost:3000
2. **Check authentication**: Try the sign-up/sign-in pages
3. **Explore dashboards**: See the different user role dashboards
4. **Customize**: Modify components as needed

## 🐛 Troubleshooting

### Common Issues:

**1. Module not found errors:**
```bash
npm install --legacy-peer-deps
```

**2. TypeScript errors:**
```bash
npm run type-check
```

**3. Firebase connection issues:**
- Check your `.env.local` file
- Ensure Firebase project is active
- Verify API keys are correct

**4. Styling issues:**
```bash
# Rebuild Tailwind
npm run build
```

## 📞 Need Help?

If you encounter any issues:
1. Check the console for error messages
2. Verify your `.env.local` file has all required values
3. Make sure Firebase project is properly configured
4. Try clearing cache and reinstalling dependencies

Your LegalPR project is ready to go! 🎉
