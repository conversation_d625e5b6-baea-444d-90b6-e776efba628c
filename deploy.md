# Deployment Guide for Hero<PERSON>

## Prerequisites
1. Heroku CLI installed
2. Git repository initialized
3. Environment variables configured

## Environment Variables for <PERSON><PERSON>

Set these environment variables in your Heroku dashboard or using the CLI:

```bash
# Firebase Configuration
heroku config:set NEXT_PUBLIC_FIREBASE_API_KEY=your_firebase_api_key
heroku config:set NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=delawpr.firebaseapp.com
heroku config:set NEXT_PUBLIC_FIREBASE_PROJECT_ID=delawpr
heroku config:set NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=delawpr.appspot.com
heroku config:set NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
heroku config:set NEXT_PUBLIC_FIREBASE_APP_ID=your_app_id

# Stripe Configuration
heroku config:set NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_live_your_stripe_publishable_key
heroku config:set STRIPE_SECRET_KEY=sk_live_your_stripe_secret_key
heroku config:set STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# App Configuration
heroku config:set NODE_ENV=production
```

## Deployment Steps

1. **Create Heroku App**
```bash
heroku create your-app-name
```

2. **Set Buildpack**
```bash
heroku buildpacks:set heroku/nodejs
```

3. **Deploy**
```bash
git add .
git commit -m "Deploy to Heroku"
git push heroku main
```

4. **Open App**
```bash
heroku open
```

## Troubleshooting

- Check logs: `heroku logs --tail`
- Restart app: `heroku restart`
- Check config: `heroku config`

## Important Notes

- The app will automatically redirect to `/es` (Spanish) as the default locale
- All routes are internationalized with `/es` and `/en` prefixes
- PWA features are enabled for mobile installation
- Firebase authentication is configured for the delawpr project
